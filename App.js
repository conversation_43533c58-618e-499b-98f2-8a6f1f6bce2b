import { useFonts } from "expo-font";
import { StatusBar } from "expo-status-bar";
import { StyleSheet, View } from "react-native";
import { SafeAreaProvider } from "react-native-safe-area-context";

export default function App() {
  const [fontsLoaded] = useFonts({
    nunitoBold: require("../assets/fonts/nunito/Nunito-Bold.ttf"),
    nunitoExtraBold: require("../assets/fonts/nunito/Nunito-ExtraBold.ttf"),
  });
  if (!fontsLoaded) return null;

  return (
    <SafeAreaProvider>
      <View>
        <StatusBar style="light" />
      </View>
    </SafeAreaProvider>
  );
}
